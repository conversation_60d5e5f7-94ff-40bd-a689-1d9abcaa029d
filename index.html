<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>No Seat, No Peace - Shandong Dining Etiquette Game</title>
    <style>
        /* Import Google Fonts for cartoon-like appearance */
        @import url('https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700;800&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Nunito', sans-serif;
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 50%, #fd79a8 100%);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        /* Traditional Chinese pattern overlay */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 20% 20%, rgba(255,255,255,0.1) 2px, transparent 2px),
                radial-gradient(circle at 80% 80%, rgba(255,255,255,0.1) 2px, transparent 2px);
            background-size: 50px 50px;
            pointer-events: none;
            z-index: -1;
        }

        /* Screen Management */
        .screen {
            display: none;
            min-height: 100vh;
            padding: 20px;
            animation: fadeIn 0.5s ease-in-out;
        }

        .screen.active {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Header Styling */
        #gameHeader {
            text-align: center;
            margin-bottom: 2rem;
            position: relative;
        }

        #gameTitle {
            font-size: 3rem;
            font-weight: 800;
            color: #2d3436;
            text-shadow: 3px 3px 0px #fdcb6e, 6px 6px 0px #e17055;
            margin-bottom: 0.5rem;
            transform: rotate(-2deg);
        }

        #gameSubtitle {
            font-size: 1.8rem;
            font-weight: 600;
            color: #636e72;
            margin-bottom: 0.5rem;
            transform: rotate(1deg);
        }

        #gameDescription {
            font-size: 1.1rem;
            color: #74b9ff;
            font-weight: 600;
            transform: rotate(-0.5deg);
        }

        /* Button Styling - Cartoon Hand-drawn Style */
        .menu-btn, .mode-btn, .control-btn {
            background: #ffffff;
            border: 4px solid #2d3436;
            border-radius: 25px;
            padding: 15px 30px;
            font-family: 'Nunito', sans-serif;
            font-size: 1.1rem;
            font-weight: 700;
            color: #2d3436;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            box-shadow: 0 6px 0 #636e72;
            margin: 10px;
            transform: rotate(-1deg);
        }

        .menu-btn:hover, .mode-btn:hover, .control-btn:hover {
            transform: rotate(0deg) translateY(-2px);
            box-shadow: 0 8px 0 #636e72;
        }

        .menu-btn:active, .mode-btn:active, .control-btn:active {
            transform: rotate(0deg) translateY(4px);
            box-shadow: 0 2px 0 #636e72;
        }

        .menu-btn.primary, .control-btn.primary {
            background: #00b894;
            color: white;
            box-shadow: 0 6px 0 #00a085;
        }

        .menu-btn.primary:hover, .control-btn.primary:hover {
            box-shadow: 0 8px 0 #00a085;
        }

        .menu-btn.primary:active, .control-btn.primary:active {
            box-shadow: 0 2px 0 #00a085;
        }

        /* Main Menu Styling */
        .menu-content {
            text-align: center;
            max-width: 500px;
            width: 100%;
        }

        .menu-buttons {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 3rem;
        }

        .cultural-decoration {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-top: 2rem;
        }

        .decoration-item {
            font-size: 3rem;
            animation: bounce 2s infinite;
            filter: drop-shadow(3px 3px 0px rgba(0,0,0,0.3));
        }

        .decoration-item:nth-child(2) {
            animation-delay: 0.5s;
        }

        .decoration-item:nth-child(3) {
            animation-delay: 1s;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0) rotate(0deg); }
            40% { transform: translateY(-20px) rotate(5deg); }
            60% { transform: translateY(-10px) rotate(-3deg); }
        }

        /* Tutorial Screen Styling */
        .tutorial-content {
            max-width: 800px;
            width: 100%;
            background: rgba(255,255,255,0.95);
            border: 4px solid #2d3436;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 0 #636e72;
            transform: rotate(-0.5deg);
        }

        .tutorial-content h3 {
            font-size: 2rem;
            color: #2d3436;
            text-align: center;
            margin-bottom: 2rem;
            text-shadow: 2px 2px 0px #fdcb6e;
        }

        .tutorial-sections {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .tutorial-section {
            background: #f8f9fa;
            border: 3px solid #74b9ff;
            border-radius: 15px;
            padding: 1.5rem;
            transform: rotate(0.5deg);
        }

        .tutorial-section:nth-child(2) {
            transform: rotate(-0.5deg);
        }

        .tutorial-section h4 {
            color: #0984e3;
            font-size: 1.3rem;
            margin-bottom: 1rem;
            text-shadow: 1px 1px 0px rgba(255,255,255,0.8);
        }

        .tutorial-section p {
            margin-bottom: 0.5rem;
            color: #2d3436;
            font-weight: 600;
        }

        /* Game Mode Selection Styling */
        .mode-content {
            text-align: center;
            max-width: 600px;
            width: 100%;
        }

        .mode-content h3 {
            font-size: 2.5rem;
            color: #2d3436;
            margin-bottom: 2rem;
            text-shadow: 2px 2px 0px #fdcb6e;
            transform: rotate(-1deg);
        }

        .mode-buttons {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .mode-btn {
            width: 200px;
            height: 200px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            border-radius: 30px;
            transform: rotate(-2deg);
        }

        .mode-btn:nth-child(2) {
            transform: rotate(2deg);
        }

        .mode-btn:hover {
            transform: rotate(0deg) scale(1.05);
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
        }

        .mode-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            filter: drop-shadow(2px 2px 0px rgba(0,0,0,0.3));
        }

        .mode-title {
            font-size: 1.3rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
        }

        .mode-subtitle {
            font-size: 1rem;
            font-weight: 600;
            opacity: 0.9;
        }

        /* Game Screen Styling */
        #gameScreen {
            padding: 10px;
        }

        #gameUI {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: rgba(255,255,255,0.9);
            border: 3px solid #2d3436;
            border-radius: 15px;
            padding: 1rem;
            margin-bottom: 5rem;    
            box-shadow: 0 4px 0 #636e72;
        }

        #scorePanel {
            display: flex;
            gap: 2rem;
        }

        #scorePanel > div {
            font-weight: 700;
            color: #2d3436;
            font-size: 1.1rem;
        }

        #hintPanel {
            flex: 1;
            text-align: center;
        }

        #currentHint {
            font-weight: 600;
            color: #0984e3;
            font-style: italic;
        }

        /* Game Area Styling */
        #gameArea {
            display: flex;
            gap: 2rem;
            height: 60vh;
            min-height: 400px;
        }

        #tableContainer {
            flex: 2;
            position: relative;
            background: rgba(255,255,255,0.9);
            border: 4px solid #2d3436;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 6px 0 #636e72;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        /* Table Styling */
        #table {
            position: relative;
            background: #8b4513;
            border: 6px solid #654321;
            box-shadow: inset 0 0 20px rgba(0,0,0,0.3);
        }

        .circular-table {
            width: 300px;
            height: 300px;
            border-radius: 50%;
        }

        .rectangular-table {
            width: 350px;
            height: 200px;
            border-radius: 15px;
        }

        /* Seat Styling */
        .seat {
            position: absolute;
            width: 50px;
            height: 50px;
            background: #f39c12;
            border: 3px solid #e67e22;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 1.5rem;
            box-shadow: 0 3px 0 #d35400;
        }

        .seat:hover {
            transform: scale(1.1);
            background: #e74c3c;
            border-color: #c0392b;
        }

        .seat.occupied {
            background: #27ae60;
            border-color: #229954;
            box-shadow: 0 3px 0 #1e8449;
        }

        .seat.honor-seat {
            background: #f1c40f;
            border-color: #f39c12;
            box-shadow: 0 3px 0 #e67e22, 0 0 15px rgba(241,196,15,0.5);
            animation: glow 2s infinite alternate;
        }

        @keyframes glow {
            from { box-shadow: 0 3px 0 #e67e22, 0 0 15px rgba(241,196,15,0.5); }
            to { box-shadow: 0 3px 0 #e67e22, 0 0 25px rgba(241,196,15,0.8); }
        }

        /* Door Indicator */
        #doorIndicator {
            position: absolute;
            top: -50px;
            left: 50%;
            transform: translateX(-50%);
            background: #e74c3c;
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: 700;
            border: 3px solid #c0392b;
            box-shadow: 0 3px 0 #a93226;
        }

        /* NPC Panel Styling */
        #npcPanel {
            flex: 1;
            background: rgba(255,255,255,0.9);
            border: 4px solid #2d3436;
            border-radius: 20px;
            padding: 1.5rem;
            box-shadow: 0 6px 0 #636e72;
        }

        #npcPanel h4 {
            color: #2d3436;
            font-size: 1.3rem;
            margin-bottom: 1rem;
            text-align: center;
            text-shadow: 1px 1px 0px #fdcb6e;
        }

        #npcContainer {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            max-height: 400px;
            overflow-y: auto;
        }
        #npcContainer::-webkit-scrollbar {
            display: none;
        }

        #npcContainer::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        #npcContainer::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 5px;
        }

        #npcContainer::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        /* NPC Character Styling */
        .npc {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            border: 3px solid #2d3436;
            border-radius: 15px;
            padding: 1rem;
            cursor: grab;
            transition: all 0.3s ease;
            color: white;
            font-weight: 700;
            text-align: center;
            position: relative;
            transform: rotate(-1deg);
        }

        .npc:nth-child(even) {
            transform: rotate(1deg);
        }

        .npc:hover {
            transform: rotate(0deg) scale(1.05);
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
        }

        .npc:active {
            cursor: grabbing;
            transform: rotate(0deg) scale(0.95);
        }

        .npc-emoji {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            display: block;
        }

        .npc-title {
            font-size: 1rem;
            margin-bottom: 0.3rem;
        }

        .npc-description {
            font-size: 0.8rem;
            opacity: 0.9;
        }

        /* Dialogue Box Styling */
        #dialogueBox {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(255,255,255,0.95);
            border: 4px solid #2d3436;
            border-radius: 20px;
            padding: 1.5rem;
            max-width: 600px;
            width: 90%;
            box-shadow: 0 6px 0 #636e72;
            display: none;
            z-index: 1000;
        }

        #dialogueBox.active {
            display: block;
            animation: slideUp 0.5s ease-out;
        }

        @keyframes slideUp {
            from { transform: translateX(-50%) translateY(100%); }
            to { transform: translateX(-50%) translateY(0); }
        }

        #speakerName {
            font-weight: 800;
            color: #e74c3c;
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
        }

        #dialogueText {
            color: #2d3436;
            font-weight: 600;
            line-height: 1.4;
            margin-bottom: 1rem;
        }

        #nextDialogueBtn {
            background: #00b894;
            color: white;
            border: 3px solid #00a085;
            border-radius: 15px;
            padding: 10px 20px;
            font-weight: 700;
            cursor: pointer;
            float: right;
            box-shadow: 0 3px 0 #00a085;
        }

        /* Game Controls */
        #gameControls {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-top: 1rem;
            flex-wrap: wrap;
        }

        /* Results Screen Styling */
        .results-content {
            text-align: center;
            max-width: 500px;
            width: 100%;
            background: rgba(255,255,255,0.95);
            border: 4px solid #2d3436;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 0 #636e72;
            transform: rotate(-0.5deg);
        }

        #resultsTitle {
            font-size: 2.5rem;
            color: #00b894;
            margin-bottom: 2rem;
            text-shadow: 2px 2px 0px #fdcb6e;
        }

        #resultsStats {
            margin-bottom: 2rem;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 1rem;
            font-size: 1.2rem;
            font-weight: 700;
        }

        .stat-label {
            color: #636e72;
        }

        .stat-value {
            color: #00b894;
        }

        .results-buttons {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        /* Easter Egg Screen Styling */
        .easter-egg-content {
            text-align: center;
            max-width: 500px;
            width: 100%;
            background: rgba(255,255,255,0.95);
            border: 4px solid #e74c3c;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 0 #c0392b;
            transform: rotate(-1deg);
        }

        .easter-egg-content h3 {
            font-size: 2.5rem;
            color: #e74c3c;
            margin-bottom: 2rem;
            text-shadow: 2px 2px 0px #fdcb6e;
        }

        .easter-egg-image {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 2rem;
            margin-bottom: 2rem;
            font-size: 4rem;
        }

        .dog-emoji {
            animation: wiggle 1s infinite;
        }

        @keyframes wiggle {
            0%, 100% { transform: rotate(-3deg); }
            50% { transform: rotate(3deg); }
        }

        .easter-egg-text {
            font-size: 1.1rem;
            color: #2d3436;
            line-height: 1.6;
            margin-bottom: 2rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            #gameTitle {
                font-size: 2rem;
            }

            #gameSubtitle {
                font-size: 1.3rem;
            }

            .tutorial-sections {
                grid-template-columns: 1fr;
            }

            .mode-buttons {
                flex-direction: column;
                align-items: center;
            }

            #gameArea {
                flex-direction: column;
                height: auto;
            }

            #tableContainer {
                height: 300px;
            }

            .circular-table {
                width: 200px;
                height: 200px;
            }

            .rectangular-table {
                width: 250px;
                height: 150px;
            }

            .seat {
                width: 40px;
                height: 40px;
                font-size: 1.2rem;
            }

            #gameControls {
                justify-content: center;
            }

            .control-btn {
                padding: 10px 15px;
                font-size: 0.9rem;
            }
        }

        @media (max-width: 480px) {
            .screen {
                padding: 10px;
            }

            #gameTitle {
                font-size: 1.5rem;
            }

            .menu-btn, .control-btn {
                padding: 12px 20px;
                font-size: 1rem;
            }

            .mode-btn {
                width: 150px;
                height: 150px;
            }

            .mode-icon {
                font-size: 3rem;
            }
        }

        /* Additional Animations */
        @keyframes slideInRight {
            from { transform: translateX(100%); }
            to { transform: translateX(0); }
        }

        @keyframes dogWalk {
            0% { transform: translateX(-100px) rotate(0deg); }
            25% { transform: translateX(-50px) rotate(5deg); }
            50% { transform: translateX(0px) rotate(0deg); }
            75% { transform: translateX(50px) rotate(-5deg); }
            100% { transform: translateX(100px) rotate(0deg); }
        }

        .dog-walking {
            animation: dogWalk 3s ease-in-out infinite;
        }
    </style>
</head>
<body>
    <!-- Game Container -->
    <div id="gameContainer">
        <!-- Header -->
        <header id="gameHeader">
            <h1 id="gameTitle">没座没和平</h1>
            <h2 id="gameSubtitle">No Seat, No Peace</h2>
            <p id="gameDescription">Master the Art of Shandong Dining Etiquette</p>
        </header>

        <!-- Main Menu -->
        <div id="mainMenu" class="screen active">
            <div class="menu-content">
                <div class="menu-buttons">
                    <button id="startGameBtn" class="menu-btn primary">开始游戏 (Start Game)</button>
                    <button id="tutorialBtn" class="menu-btn">学习礼仪 (Learn Etiquette)</button>
                    <button id="settingsBtn" class="menu-btn">设置 (Settings)</button>
                </div>
                <div class="cultural-decoration">
                    <div class="decoration-item">🥢</div>
                    <div class="decoration-item">🍜</div>
                    <div class="decoration-item">🏮</div>
                </div>
            </div>
        </div>

        <!-- Tutorial Screen -->
        <div id="tutorialScreen" class="screen">
            <div class="tutorial-content">
                <h3>山东餐桌礼仪基础 (Shandong Dining Etiquette Basics)</h3>
                <div class="tutorial-sections">
                    <div class="tutorial-section">
                        <h4>圆桌规则 (Round Table Rules)</h4>
                        <p>• 面向门口的座位是主位 (Seat facing entrance is the honor seat)</p>
                        <p>• 主位左侧：2、4、6位 (Left side: 2nd, 4th, 6th importance)</p>
                        <p>• 主位右侧：3、5、7位 (Right side: 3rd, 5th, 7th importance)</p>
                    </div>
                    <div class="tutorial-section">
                        <h4>方桌规则 (Rectangular Table Rules)</h4>
                        <p>• 面向门口右手边是主位 (Right seat facing entrance is honor)</p>
                        <p>• 避免让客人坐在上菜位置 (Avoid seating guests in serving positions)</p>
                    </div>
                </div>
                <button id="closeTutorialBtn" class="menu-btn">返回 (Back)</button>
            </div>
        </div>

        <!-- Game Mode Selection -->
        <div id="gameModeScreen" class="screen">
            <div class="mode-content">
                <h3>选择游戏模式 (Choose Game Mode)</h3>
                <div class="mode-buttons">
                    <button id="circularModeBtn" class="mode-btn">
                        <div class="mode-icon">⭕</div>
                        <div class="mode-title">圆桌模式</div>
                        <div class="mode-subtitle">Circular Table</div>
                    </button>
                    <button id="rectangularModeBtn" class="mode-btn">
                        <div class="mode-icon">⬜</div>
                        <div class="mode-title">方桌模式</div>
                        <div class="mode-subtitle">Rectangular Table</div>
                    </button>
                </div>
                <button id="backToMenuBtn" class="menu-btn">返回主菜单 (Back to Menu)</button>
            </div>
        </div>

        <!-- Game Screen -->
        <div id="gameScreen" class="screen">
            <!-- Game UI -->
            <div id="gameUI">
                <div id="scorePanel">
                    <div id="score">Score: <span id="scoreValue">0</span></div>
                    <div id="level">Level: <span id="levelValue">1</span></div>
                    <div id="mistakes">Mistakes: <span id="mistakeValue">0</span>/3</div>
                </div>
                <div id="hintPanel">
                    <div id="currentHint">Drag NPCs to appropriate seats based on their social status</div>
                </div>
            </div>

            <!-- Game Area -->
            <div id="gameArea">
                <!-- Table Container -->
                <div id="tableContainer">
                    <div id="table" class="circular-table">
                        <!-- Seats will be dynamically generated -->
                    </div>
                    <div id="doorIndicator">
                        <span>🚪 门 (Door)</span>
                    </div>
                </div>

                <!-- NPC Panel -->
                <div id="npcPanel">
                    <h4>Characters to Seat:</h4>
                    <div id="npcContainer">
                        <!-- NPCs will be dynamically generated -->
                    </div>
                </div>
            </div>

            <!-- Dialogue Box -->
            <div id="dialogueBox">
                <div id="dialogueContent">
                    <div id="speakerName"></div>
                    <div id="dialogueText"></div>
                </div>
                <button id="nextDialogueBtn">继续 (Continue)</button>
            </div>

            <!-- Game Controls -->
            <div id="gameControls">
                <button id="checkSeatingBtn" class="control-btn primary">检查座位 (Check Seating)</button>
                <button id="resetSeatingBtn" class="control-btn">重置 (Reset)</button>
                <button id="hintBtn" class="control-btn">提示 (Hint)</button>
                <button id="pauseGameBtn" class="control-btn">暂停 (Pause)</button>
            </div>
        </div>

        <!-- Results Screen -->
        <div id="resultsScreen" class="screen">
            <div class="results-content">
                <h3 id="resultsTitle">Level Complete!</h3>
                <div id="resultsStats">
                    <div class="stat-item">
                        <span class="stat-label">Score:</span>
                        <span class="stat-value" id="finalScore">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Accuracy:</span>
                        <span class="stat-value" id="accuracy">100%</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Time:</span>
                        <span class="stat-value" id="completionTime">0:00</span>
                    </div>
                </div>
                <div class="results-buttons">
                    <button id="nextLevelBtn" class="menu-btn primary">下一关 (Next Level)</button>
                    <button id="retryLevelBtn" class="menu-btn">重试 (Retry)</button>
                    <button id="backToMenuFromResultsBtn" class="menu-btn">主菜单 (Main Menu)</button>
                </div>
            </div>
        </div>

        <!-- Easter Egg Screen -->
        <div id="easterEggScreen" class="screen">
            <div class="easter-egg-content">
                <h3>🐕 特殊结局 (Special Ending)</h3>
                <div class="easter-egg-image">
                    <div class="dog-emoji">🐕</div>
                    <div class="host-seat">🪑</div>
                </div>
                <p class="easter-egg-text">
                    连续三次错误安排后，小狗坐上了主位！<br>
                    After three consecutive mistakes, the dog takes the host seat!<br>
                    <em>"汪汪！我来主持这场宴会！"</em>
                </p>
                <button id="restartFromEasterEggBtn" class="menu-btn">重新开始 (Restart)</button>
            </div>
        </div>
    </div>

    <script>
        // Game State Management System
        console.log("No Seat, No Peace - Game Loading...");

        // Game Configuration
        const GAME_CONFIG = {
            MAX_MISTAKES: 3,
            EASTER_EGG_THRESHOLD: 3,
            SCORE_MULTIPLIER: 100,
            TIME_BONUS: 10,
            LEVELS: {
                1: { tableType: 'circular', npcCount: 4, doorPosition: 'top', difficulty: 'easy' },
                2: { tableType: 'circular', npcCount: 6, doorPosition: 'right', difficulty: 'easy' },
                3: { tableType: 'rectangular', npcCount: 4, doorPosition: 'top', difficulty: 'medium' },
                4: { tableType: 'rectangular', npcCount: 6, doorPosition: 'left', difficulty: 'medium' },
                5: { tableType: 'circular', npcCount: 8, doorPosition: 'bottom', difficulty: 'hard' },
                6: { tableType: 'rectangular', npcCount: 8, doorPosition: 'right', difficulty: 'hard' },
                7: { tableType: 'circular', npcCount: 6, doorPosition: 'left', difficulty: 'expert' },
                8: { tableType: 'rectangular', npcCount: 6, doorPosition: 'bottom', difficulty: 'expert' }
            }
        };

        // Game State Class
        class GameState {
            constructor() {
                this.currentLevel = 1;
                this.score = 0;
                this.mistakes = 0;
                this.consecutiveMistakes = 0;
                this.gameMode = null; // 'circular' or 'rectangular'
                this.startTime = null;
                this.endTime = null;
                this.isGameActive = false;
                this.currentNPCs = [];
                this.seatedNPCs = new Map(); // seat -> npc mapping
                this.doorPosition = 'top';
            }

            startLevel(level) {
                this.currentLevel = level;
                this.mistakes = 0;
                this.startTime = Date.now();
                this.isGameActive = true;
                this.seatedNPCs.clear();

                const levelConfig = GAME_CONFIG.LEVELS[level];
                this.gameMode = levelConfig.tableType;
                this.doorPosition = levelConfig.doorPosition;

                this.updateUI();
            }

            addMistake() {
                this.mistakes++;
                this.consecutiveMistakes++;
                this.updateUI();

                if (this.consecutiveMistakes >= GAME_CONFIG.EASTER_EGG_THRESHOLD) {
                    this.triggerEasterEgg();
                    return true;
                }

                if (this.mistakes >= GAME_CONFIG.MAX_MISTAKES) {
                    this.endLevel(false);
                    return true;
                }

                return false;
            }

            addScore(points) {
                this.score += points;
                this.updateUI();
            }

            endLevel(success) {
                this.endTime = Date.now();
                this.isGameActive = false;

                if (success) {
                    this.consecutiveMistakes = 0;
                    const timeBonus = Math.max(0, GAME_CONFIG.TIME_BONUS - Math.floor((this.endTime - this.startTime) / 1000));
                    this.addScore(GAME_CONFIG.SCORE_MULTIPLIER + timeBonus);
                }
            }

            triggerEasterEgg() {
                this.isGameActive = false;
                showScreen('easterEgg');
            }

            reset() {
                this.currentLevel = 1;
                this.score = 0;
                this.mistakes = 0;
                this.consecutiveMistakes = 0;
                this.gameMode = null;
                this.isGameActive = false;
                this.seatedNPCs.clear();
                this.updateUI();
            }

            updateUI() {
                document.getElementById('scoreValue').textContent = this.score;
                document.getElementById('levelValue').textContent = this.currentLevel;
                document.getElementById('mistakeValue').textContent = this.mistakes;
            }

            getCompletionTime() {
                if (this.startTime && this.endTime) {
                    const seconds = Math.floor((this.endTime - this.startTime) / 1000);
                    const minutes = Math.floor(seconds / 60);
                    return `${minutes}:${(seconds % 60).toString().padStart(2, '0')}`;
                }
                return '0:00';
            }

            getAccuracy() {
                const totalAttempts = this.mistakes + 1; // +1 for successful completion
                return Math.round((1 / totalAttempts) * 100);
            }
        }

        // Level Manager Class
        class LevelManager {
            constructor(gameState) {
                this.gameState = gameState;
            }

            loadLevel(levelNumber) {
                const levelConfig = GAME_CONFIG.LEVELS[levelNumber];
                if (!levelConfig) {
                    console.error('Level not found:', levelNumber);
                    return false;
                }

                this.gameState.startLevel(levelNumber);
                this.setupTable(levelConfig);
                this.generateNPCs(levelConfig.npcCount);
                this.updateHint(levelConfig);

                return true;
            }

            setupTable(config) {
                const table = document.getElementById('table');
                const doorIndicator = document.getElementById('doorIndicator');

                // Clear existing seats
                table.innerHTML = '';

                // Set table type
                table.className = config.tableType + '-table';

                // Position door indicator
                this.positionDoor(doorIndicator, config.doorPosition);

                // Generate seats based on table type
                if (config.tableType === 'circular') {
                    this.generateCircularSeats(table, config.npcCount + 2); // Extra seats for challenge
                } else {
                    this.generateRectangularSeats(table, config.npcCount + 2);
                }
            }

            positionDoor(doorElement, position) {
                doorElement.className = 'door-' + position;

                switch(position) {
                    case 'top':
                        doorElement.style.top = '-50px';
                        doorElement.style.left = '50%';
                        doorElement.style.transform = 'translateX(-50%)';
                        break;
                    case 'right':
                        doorElement.style.top = '50%';
                        doorElement.style.right = '-50px';
                        doorElement.style.left = 'auto';
                        doorElement.style.transform = 'translateY(-50%)';
                        break;
                    case 'bottom':
                        doorElement.style.bottom = '-50px';
                        doorElement.style.top = 'auto';
                        doorElement.style.left = '50%';
                        doorElement.style.transform = 'translateX(-50%)';
                        break;
                    case 'left':
                        doorElement.style.top = '50%';
                        doorElement.style.left = '-50px';
                        doorElement.style.transform = 'translateY(-50%)';
                        break;
                }
            }

            generateCircularSeats(table, seatCount) {
                const radius = 120;
                const centerX = 150;
                const centerY = 150;

                for (let i = 0; i < seatCount; i++) {
                    const angle = (i * 2 * Math.PI) / seatCount;
                    const x = centerX + radius * Math.cos(angle) - 25;
                    const y = centerY + radius * Math.sin(angle) - 25;

                    const seat = this.createSeat(i, x, y);

                    // Mark honor seat based on door position
                    if (this.isHonorSeat(i, seatCount, this.gameState.doorPosition)) {
                        seat.classList.add('honor-seat');
                        seat.dataset.isHonor = 'true';
                    }

                    table.appendChild(seat);
                }
            }

            generateRectangularSeats(table, seatCount) {
                const positions = [
                    { x: 50, y: 25 },   // Top left
                    { x: 150, y: 25 },  // Top center
                    { x: 250, y: 25 },  // Top right
                    { x: 250, y: 125 }, // Right center
                    { x: 150, y: 175 }, // Bottom center
                    { x: 50, y: 175 },  // Bottom left
                    { x: 25, y: 125 },  // Left center
                    { x: 25, y: 75 }    // Left upper
                ];

                for (let i = 0; i < Math.min(seatCount, positions.length); i++) {
                    const pos = positions[i];
                    const seat = this.createSeat(i, pos.x - 25, pos.y - 25);

                    // Mark honor seat for rectangular table
                    if (this.isRectangularHonorSeat(i, this.gameState.doorPosition)) {
                        seat.classList.add('honor-seat');
                        seat.dataset.isHonor = 'true';
                    }

                    table.appendChild(seat);
                }
            }

            createSeat(index, x, y) {
                const seat = document.createElement('div');
                seat.className = 'seat';
                seat.dataset.seatIndex = index;
                seat.style.left = x + 'px';
                seat.style.top = y + 'px';

                // Add drop zone functionality
                seat.addEventListener('dragover', this.handleDragOver.bind(this));
                seat.addEventListener('drop', this.handleDrop.bind(this));
                seat.addEventListener('click', this.handleSeatClick.bind(this));

                return seat;
            }

            isHonorSeat(seatIndex, totalSeats, doorPosition) {
                // For circular tables, honor seat faces the door
                const doorAngles = {
                    'top': 0,
                    'right': Math.PI / 2,
                    'bottom': Math.PI,
                    'left': 3 * Math.PI / 2
                };

                const seatAngle = (seatIndex * 2 * Math.PI) / totalSeats;
                const doorAngle = doorAngles[doorPosition];
                const angleDiff = Math.abs(seatAngle - doorAngle);

                return angleDiff < Math.PI / totalSeats || angleDiff > (2 * Math.PI - Math.PI / totalSeats);
            }

            isRectangularHonorSeat(seatIndex, doorPosition) {
                // For rectangular tables, honor seat is right side facing entrance
                const honorPositions = {
                    'top': 2,    // Top right when door is at top
                    'right': 4,  // Bottom center when door is at right
                    'bottom': 5, // Bottom left when door is at bottom
                    'left': 1    // Top center when door is at left
                };

                return seatIndex === honorPositions[doorPosition];
            }

            updateHint(config) {
                const hints = {
                    circular: "在圆桌上，面向门口的座位是主位。左侧座位按2、4、6排序，右侧按3、5、7排序。",
                    rectangular: "在方桌上，面向门口右手边的座位是主位。避免让客人坐在上菜的位置。"
                };

                document.getElementById('currentHint').textContent = hints[config.tableType];
            }

            handleDragOver(event) {
                event.preventDefault();
                event.currentTarget.style.backgroundColor = '#f39c12';
            }

            handleDrop(event) {
                event.preventDefault();
                const seat = event.currentTarget;
                const npcId = event.dataTransfer.getData('text/plain');

                // Reset seat appearance
                seat.style.backgroundColor = '';

                // Check if seat is already occupied
                if (seat.classList.contains('occupied')) {
                    this.showFeedback('这个座位已经有人了！', 'warning');
                    return;
                }

                // Place NPC in seat
                this.placeNPCInSeat(npcId, seat);
            }

            handleSeatClick(event) {
                const seat = event.currentTarget;

                // If seat is occupied, show NPC info
                if (seat.classList.contains('occupied')) {
                    const npcId = seat.dataset.occupiedBy;
                    const npc = NPCManager.getNPCById(npcId);
                    if (npc) {
                        this.showNPCDialogue(npc, npc.getHintDialogue());
                    }
                    return;
                }

                // For mobile: show available NPCs for this seat
                this.showMobileNPCSelector(seat);
            }

            placeNPCInSeat(npcId, seat) {
                const npc = NPCManager.getNPCById(npcId);
                if (!npc) return;

                // Update seat appearance
                seat.classList.add('occupied');
                seat.innerHTML = npc.emoji;
                seat.dataset.occupiedBy = npcId;

                // Update game state
                const seatIndex = parseInt(seat.dataset.seatIndex);
                this.gameState.seatedNPCs.set(seatIndex, npc);

                // Hide the NPC from the panel
                const npcElement = document.querySelector(`[data-npc-id="${npcId}"]`);
                if (npcElement) {
                    npcElement.style.display = 'none';
                }

                // Show feedback
                this.showFeedback(`${npc.name} 已就座`, 'success');
            }

            showFeedback(message, type = 'info') {
                // Create feedback element
                const feedback = document.createElement('div');
                feedback.className = `feedback feedback-${type}`;
                feedback.textContent = message;
                feedback.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: ${type === 'success' ? '#00b894' : type === 'warning' ? '#fdcb6e' : '#74b9ff'};
                    color: white;
                    padding: 10px 20px;
                    border-radius: 10px;
                    font-weight: 700;
                    z-index: 1000;
                    animation: slideInRight 0.3s ease-out;
                `;

                document.body.appendChild(feedback);

                // Remove after 3 seconds
                setTimeout(() => {
                    feedback.remove();
                }, 3000);
            }

            showMobileNPCSelector(seat) {
                // Create mobile selector overlay
                const overlay = document.createElement('div');
                overlay.className = 'mobile-selector-overlay';
                overlay.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0,0,0,0.8);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 2000;
                `;

                const selector = document.createElement('div');
                selector.className = 'mobile-npc-selector';
                selector.style.cssText = `
                    background: white;
                    border-radius: 20px;
                    padding: 20px;
                    max-width: 300px;
                    width: 90%;
                `;

                selector.innerHTML = `
                    <h3 style="text-align: center; margin-bottom: 20px;">选择角色 (Select Character)</h3>
                    <div class="mobile-npc-list"></div>
                    <button class="mobile-cancel-btn" style="width: 100%; margin-top: 15px; padding: 10px; background: #e74c3c; color: white; border: none; border-radius: 10px; font-weight: 700;">取消 (Cancel)</button>
                `;

                const npcList = selector.querySelector('.mobile-npc-list');

                // Add available NPCs
                this.gameState.currentNPCs.forEach(npc => {
                    const npcElement = document.querySelector(`[data-npc-id="${npc.id}"]`);
                    if (npcElement && npcElement.style.display !== 'none') {
                        const npcButton = document.createElement('button');
                        npcButton.style.cssText = `
                            width: 100%;
                            margin-bottom: 10px;
                            padding: 15px;
                            background: #74b9ff;
                            color: white;
                            border: none;
                            border-radius: 10px;
                            font-weight: 700;
                            display: flex;
                            align-items: center;
                            gap: 10px;
                        `;
                        npcButton.innerHTML = `<span style="font-size: 1.5rem;">${npc.emoji}</span> ${npc.name}`;

                        npcButton.addEventListener('click', () => {
                            this.placeNPCInSeat(npc.id, seat);
                            overlay.remove();
                        });

                        npcList.appendChild(npcButton);
                    }
                });

                // Cancel button
                selector.querySelector('.mobile-cancel-btn').addEventListener('click', () => {
                    overlay.remove();
                });

                overlay.appendChild(selector);
                document.body.appendChild(overlay);
            }

            showNPCDialogue(npc, message) {
                const dialogueBox = document.getElementById('dialogueBox');
                const speakerName = document.getElementById('speakerName');
                const dialogueText = document.getElementById('dialogueText');

                speakerName.textContent = npc.name;
                dialogueText.textContent = message;

                dialogueBox.classList.add('active');

                // Auto-hide after 5 seconds
                setTimeout(() => {
                    dialogueBox.classList.remove('active');
                }, 5000);
            }

            generateNPCs(npcCount) {
                const npcContainer = document.getElementById('npcContainer');
                npcContainer.innerHTML = '';

                const selectedNPCs = NPCManager.getRandomNPCs(npcCount);
                this.gameState.currentNPCs = selectedNPCs;

                selectedNPCs.forEach((npc, index) => {
                    const npcElement = NPCManager.createNPCElement(npc, index);
                    npcContainer.appendChild(npcElement);
                });
            }
        }

        // NPC Character System
        class NPC {
            constructor(id, name, title, emoji, status, priority, dialogues) {
                this.id = id;
                this.name = name;
                this.title = title;
                this.emoji = emoji;
                this.status = status; // 'leader', 'elder', 'guest', 'young', 'host'
                this.priority = priority; // 1 = highest priority (honor seat)
                this.dialogues = dialogues;
            }

            getCorrectPlacementDialogue() {
                return this.dialogues.correct[Math.floor(Math.random() * this.dialogues.correct.length)];
            }

            getIncorrectPlacementDialogue() {
                return this.dialogues.incorrect[Math.floor(Math.random() * this.dialogues.incorrect.length)];
            }

            getHintDialogue() {
                return this.dialogues.hint || "请根据我的身份安排合适的座位。";
            }
        }

        // NPC Manager Class
        class NPCManager {
            static npcDatabase = [
                new NPC('leader1', '王局长', '政府领导', '👔', 'leader', 1, {
                    correct: [
                        "好！这个位置正合适，我可以照顾到所有人。",
                        "不错，这里能看到全局，很好。",
                        "这个座位安排得当，体现了礼仪。"
                    ],
                    incorrect: [
                        "这个座位...你是想让我提前退休吗？",
                        "小同志，这样安排不太合适吧？",
                        "我觉得这个位置不太对劲..."
                    ],
                    hint: "我是领导，应该坐在能照顾全局的位置。"
                }),

                new NPC('elder1', '张大爷', '德高望重长者', '👴', 'elder', 2, {
                    correct: [
                        "好孩子，你很懂礼数！",
                        "这样安排很好，尊老爱幼嘛。",
                        "不错不错，年轻人有礼貌。"
                    ],
                    incorrect: [
                        "年轻人，你爹妈没教过你礼貌吗？",
                        "这个座位不合适，我这把老骨头...",
                        "哎呀，这样坐着不舒服啊。"
                    ],
                    hint: "我年纪大了，需要一个受尊敬的位置。"
                }),

                new NPC('uncle1', '李大叔', '红脸大叔', '🍺', 'guest', 3, {
                    correct: [
                        "好！这个位置我喜欢，来，咱们喝一个！",
                        "不错不错，这里正好能敬酒！",
                        "这个座位安排得好，我请客！"
                    ],
                    incorrect: [
                        "这个位置不行啊，我怎么给大家敬酒？",
                        "兄弟，这样坐着不方便啊！",
                        "换个位置吧，这里不太合适。"
                    ],
                    hint: "我喜欢热闹，要能给大家敬酒的位置。"
                }),

                new NPC('young1', '小王', '年轻后辈', '📱', 'young', 5, {
                    correct: [
                        "谢谢！这个位置挺好的。",
                        "好的，我坐这里就行。",
                        "没问题，这里很合适。"
                    ],
                    incorrect: [
                        "啊？我坐这里合适吗？",
                        "这个位置会不会太显眼了？",
                        "我还是坐边上吧..."
                    ],
                    hint: "我是晚辈，应该坐在不太显眼的位置。"
                }),

                new NPC('host1', '主人家', '宴会主人', '🏠', 'host', 6, {
                    correct: [
                        "好的，我坐这里方便照顾大家。",
                        "这个位置不错，能看到所有客人。",
                        "谢谢安排，这样我能更好地招待客人。"
                    ],
                    incorrect: [
                        "我坐这里的话，怎么照顾客人呢？",
                        "这个位置不太方便我招待大家。",
                        "还是换个位置吧，我需要能服务大家的地方。"
                    ],
                    hint: "我是主人，需要能照顾所有客人的位置。"
                }),

                new NPC('guest1', '贵客', '重要客人', '🎩', 'guest', 2, {
                    correct: [
                        "非常感谢您的周到安排！",
                        "这个位置很好，体现了主人的用心。",
                        "很荣幸能坐在这里。"
                    ],
                    incorrect: [
                        "这个安排...似乎不太合适？",
                        "我觉得这个位置可能不太对。",
                        "或许我们重新考虑一下座位安排？"
                    ],
                    hint: "我是重要客人，应该得到相应的礼遇。"
                }),

                new NPC('elder2', '奶奶', '慈祥老人', '👵', 'elder', 2, {
                    correct: [
                        "好孩子，你真懂事！",
                        "这样安排很贴心，谢谢你。",
                        "不错，年轻人很有礼貌。"
                    ],
                    incorrect: [
                        "孩子，这个位置奶奶坐着不太舒服。",
                        "能不能换个位置？这里不太合适。",
                        "奶奶年纪大了，这样坐着不方便。"
                    ],
                    hint: "我是长辈，需要一个舒适受尊敬的位置。"
                }),

                new NPC('business1', '商人', '生意伙伴', '💼', 'guest', 3, {
                    correct: [
                        "很好的安排，这样谈生意很方便。",
                        "这个位置不错，我们可以好好聊聊。",
                        "谢谢，这里很适合商务交流。"
                    ],
                    incorrect: [
                        "这个位置不太利于我们的商务交流。",
                        "能否重新安排？这样不太方便谈事情。",
                        "我觉得换个位置会更好一些。"
                    ],
                    hint: "我是来谈生意的，需要一个便于交流的位置。"
                })
            ];

            static getRandomNPCs(count) {
                // Ensure we always have a good mix of different status types
                const shuffled = [...this.npcDatabase].sort(() => 0.5 - Math.random());
                return shuffled.slice(0, count);
            }

            static createNPCElement(npc, index) {
                const npcElement = document.createElement('div');
                npcElement.className = 'npc';
                npcElement.dataset.npcId = npc.id;
                npcElement.dataset.npcIndex = index;
                npcElement.draggable = true;

                npcElement.innerHTML = `
                    <span class="npc-emoji">${npc.emoji}</span>
                    <div class="npc-title">${npc.title}</div>
                    <div class="npc-description">${npc.name}</div>
                `;

                // Add drag event listeners
                npcElement.addEventListener('dragstart', this.handleDragStart.bind(this));
                npcElement.addEventListener('dragend', this.handleDragEnd.bind(this));

                return npcElement;
            }

            static handleDragStart(event) {
                event.dataTransfer.setData('text/plain', event.target.dataset.npcId);
                event.target.style.opacity = '0.5';
            }

            static handleDragEnd(event) {
                event.target.style.opacity = '1';
            }

            static getNPCById(id) {
                return this.npcDatabase.find(npc => npc.id === id);
            }
        }

        // Etiquette Validation Engine
        class EtiquetteValidator {
            constructor(gameState, levelManager) {
                this.gameState = gameState;
                this.levelManager = levelManager;
            }

            validateSeating() {
                const violations = [];
                const seatedNPCs = this.gameState.seatedNPCs;

                // Check if all NPCs are seated
                if (seatedNPCs.size !== this.gameState.currentNPCs.length) {
                    return {
                        isValid: false,
                        violations: [{
                            message: "请将所有客人安排就座！",
                            npc: null
                        }]
                    };
                }

                // Validate based on table type
                if (this.gameState.gameMode === 'circular') {
                    this.validateCircularTable(violations);
                } else {
                    this.validateRectangularTable(violations);
                }

                // Check general hierarchy rules
                this.validateHierarchy(violations);

                return {
                    isValid: violations.length === 0,
                    violations: violations
                };
            }

            validateCircularTable(violations) {
                const honorSeat = this.getHonorSeat();
                const honorNPC = this.gameState.seatedNPCs.get(honorSeat);

                // Check if highest priority person is in honor seat
                const highestPriorityNPC = this.getHighestPriorityNPC();
                if (!honorNPC || honorNPC.id !== highestPriorityNPC.id) {
                    violations.push({
                        npc: highestPriorityNPC,
                        message: highestPriorityNPC.getIncorrectPlacementDialogue()
                    });
                }

                // Check left-right hierarchy for circular table
                this.validateCircularHierarchy(violations, honorSeat);
            }

            validateRectangularTable(violations) {
                const honorSeat = this.getHonorSeat();
                const honorNPC = this.gameState.seatedNPCs.get(honorSeat);

                // Check if highest priority person is in honor seat
                const highestPriorityNPC = this.getHighestPriorityNPC();
                if (!honorNPC || honorNPC.id !== highestPriorityNPC.id) {
                    violations.push({
                        npc: highestPriorityNPC,
                        message: highestPriorityNPC.getIncorrectPlacementDialogue()
                    });
                }

                // Check for serving position violations
                this.validateServingPositions(violations);
            }

            validateCircularHierarchy(violations, honorSeatIndex) {
                const totalSeats = document.querySelectorAll('.seat').length;
                const seatedNPCs = this.gameState.seatedNPCs;

                // Get NPCs sorted by priority
                const npcsByPriority = Array.from(seatedNPCs.values()).sort((a, b) => a.priority - b.priority);

                // Check if seating follows left-right hierarchy pattern
                for (let i = 1; i < npcsByPriority.length; i++) {
                    const npc = npcsByPriority[i];
                    const seatIndex = this.getNPCSeatIndex(npc);
                    const expectedPosition = this.getExpectedCircularPosition(honorSeatIndex, i, totalSeats);

                    if (!this.isValidCircularPosition(seatIndex, expectedPosition, totalSeats)) {
                        violations.push({
                            npc: npc,
                            message: npc.getIncorrectPlacementDialogue()
                        });
                    }
                }
            }

            validateServingPositions(violations) {
                // For rectangular tables, check if anyone is in serving positions
                const servingPositions = this.getServingPositions();
                const seatedNPCs = this.gameState.seatedNPCs;

                servingPositions.forEach(position => {
                    const npc = seatedNPCs.get(position);
                    if (npc) {
                        violations.push({
                            npc: npc,
                            message: "这个位置会被汤汁溅到，不适合客人！"
                        });
                    }
                });
            }

            validateHierarchy(violations) {
                // Check general hierarchy rules
                const seatedNPCs = Array.from(this.gameState.seatedNPCs.values());

                // Leaders should not be in corner positions
                const leaders = seatedNPCs.filter(npc => npc.status === 'leader');
                leaders.forEach(leader => {
                    const seatIndex = this.getNPCSeatIndex(leader);
                    if (this.isCornerPosition(seatIndex)) {
                        violations.push({
                            npc: leader,
                            message: leader.getIncorrectPlacementDialogue()
                        });
                    }
                });

                // Elders should be in respected positions
                const elders = seatedNPCs.filter(npc => npc.status === 'elder');
                elders.forEach(elder => {
                    const seatIndex = this.getNPCSeatIndex(elder);
                    if (this.isLowStatusPosition(seatIndex)) {
                        violations.push({
                            npc: elder,
                            message: elder.getIncorrectPlacementDialogue()
                        });
                    }
                });
            }

            getHonorSeat() {
                const honorSeatElement = document.querySelector('.seat.honor-seat');
                return honorSeatElement ? parseInt(honorSeatElement.dataset.seatIndex) : 0;
            }

            getHighestPriorityNPC() {
                return this.gameState.currentNPCs.reduce((highest, npc) =>
                    npc.priority < highest.priority ? npc : highest
                );
            }

            getNPCSeatIndex(npc) {
                for (let [seatIndex, seatedNPC] of this.gameState.seatedNPCs) {
                    if (seatedNPC.id === npc.id) {
                        return seatIndex;
                    }
                }
                return -1;
            }

            getExpectedCircularPosition(honorSeat, priority, totalSeats) {
                // Left side positions: 2nd, 4th, 6th...
                // Right side positions: 3rd, 5th, 7th...
                const isLeftSide = priority % 2 === 1; // 1st, 3rd, 5th... go to left
                const positionInSide = Math.floor((priority - 1) / 2);

                if (isLeftSide) {
                    return (honorSeat - positionInSide - 1 + totalSeats) % totalSeats;
                } else {
                    return (honorSeat + positionInSide + 1) % totalSeats;
                }
            }

            isValidCircularPosition(actualSeat, expectedSeat, totalSeats) {
                // Allow some flexibility in positioning (±1 seat)
                const diff = Math.abs(actualSeat - expectedSeat);
                return diff <= 1 || diff >= totalSeats - 1;
            }

            getServingPositions() {
                // For rectangular tables, positions near the kitchen/serving area
                // This would depend on the specific table layout
                return [4, 5]; // Bottom positions typically used for serving
            }

            isCornerPosition(seatIndex) {
                // Define corner positions based on table layout
                const totalSeats = document.querySelectorAll('.seat').length;
                if (this.gameState.gameMode === 'rectangular') {
                    return [0, 2, 4, 6].includes(seatIndex); // Corner positions
                }
                return false; // Circular tables don't have corners
            }

            isLowStatusPosition(seatIndex) {
                // Define positions that are considered low status
                const totalSeats = document.querySelectorAll('.seat').length;
                const honorSeat = this.getHonorSeat();

                // Positions furthest from honor seat are low status
                const distance = Math.min(
                    Math.abs(seatIndex - honorSeat),
                    totalSeats - Math.abs(seatIndex - honorSeat)
                );

                return distance > totalSeats / 2;
            }
        }

        // Initialize game state and level manager
        const gameState = new GameState();
        const levelManager = new LevelManager(gameState);

        // Screen management
        const screens = {
            mainMenu: document.getElementById('mainMenu'),
            tutorial: document.getElementById('tutorialScreen'),
            gameMode: document.getElementById('gameModeScreen'),
            game: document.getElementById('gameScreen'),
            results: document.getElementById('resultsScreen'),
            easterEgg: document.getElementById('easterEggScreen')
        };

        function showScreen(screenName) {
            Object.values(screens).forEach(screen => {
                screen.classList.remove('active');
            });
            screens[screenName].classList.add('active');
        }

        // Basic event listeners
        document.getElementById('startGameBtn').addEventListener('click', () => {
            showScreen('gameMode');
        });

        document.getElementById('tutorialBtn').addEventListener('click', () => {
            showScreen('tutorial');
        });

        document.getElementById('closeTutorialBtn').addEventListener('click', () => {
            showScreen('mainMenu');
        });

        document.getElementById('backToMenuBtn').addEventListener('click', () => {
            showScreen('mainMenu');
        });

        document.getElementById('backToMenuFromResultsBtn').addEventListener('click', () => {
            showScreen('mainMenu');
        });

        document.getElementById('restartFromEasterEggBtn').addEventListener('click', () => {
            gameState.reset();
            showScreen('mainMenu');
        });

        // Game mode selection
        document.getElementById('circularModeBtn').addEventListener('click', () => {
            gameState.gameMode = 'circular';
            startGame();
        });

        document.getElementById('rectangularModeBtn').addEventListener('click', () => {
            gameState.gameMode = 'rectangular';
            startGame();
        });

        // Game controls
        document.getElementById('checkSeatingBtn').addEventListener('click', () => {
            checkSeatingArrangement();
        });

        document.getElementById('resetSeatingBtn').addEventListener('click', () => {
            resetSeating();
        });

        document.getElementById('hintBtn').addEventListener('click', () => {
            showHint();
        });

        document.getElementById('pauseGameBtn').addEventListener('click', () => {
            pauseGame();
        });

        // Results screen
        document.getElementById('nextLevelBtn').addEventListener('click', () => {
            nextLevel();
        });

        document.getElementById('retryLevelBtn').addEventListener('click', () => {
            retryLevel();
        });

        // Dialogue box
        document.getElementById('nextDialogueBtn').addEventListener('click', () => {
            document.getElementById('dialogueBox').classList.remove('active');
        });

        // Game functions
        function startGame() {
            showScreen('game');
            levelManager.loadLevel(gameState.currentLevel);
        }

        function checkSeatingArrangement() {
            const validator = new EtiquetteValidator(gameState, levelManager);
            const result = validator.validateSeating();

            if (result.isValid) {
                // Success!
                gameState.endLevel(true);
                showResults(result);
            } else {
                // Show feedback for mistakes
                result.violations.forEach(violation => {
                    levelManager.showNPCDialogue(violation.npc, violation.message);
                });

                const gameEnded = gameState.addMistake();
                if (!gameEnded) {
                    levelManager.showFeedback(`还有 ${result.violations.length} 个问题需要解决`, 'warning');
                }
            }
        }

        function resetSeating() {
            // Clear all seated NPCs
            gameState.seatedNPCs.clear();

            // Reset seat appearances
            document.querySelectorAll('.seat').forEach(seat => {
                seat.classList.remove('occupied');
                seat.innerHTML = '';
                seat.removeAttribute('data-occupied-by');
            });

            // Show all NPCs in the panel again
            document.querySelectorAll('.npc').forEach(npc => {
                npc.style.display = 'block';
                npc.style.position = 'static';
                npc.style.transform = '';
                npc.style.opacity = '1';
            });

            // Show feedback
            levelManager.showFeedback('座位已重置，请重新安排 (Seating reset, please rearrange)', 'info');
        }

        function showHint() {
            // Show hint from a random unseated NPC
            const unseatedNPCs = gameState.currentNPCs.filter(npc => {
                const npcElement = document.querySelector(`[data-npc-id="${npc.id}"]`);
                return npcElement && npcElement.style.display !== 'none';
            });

            if (unseatedNPCs.length > 0) {
                const randomNPC = unseatedNPCs[Math.floor(Math.random() * unseatedNPCs.length)];
                levelManager.showNPCDialogue(randomNPC, randomNPC.getHintDialogue());
            } else {
                levelManager.showFeedback('所有客人都已就座，检查座位安排是否正确！', 'info');
            }
        }

        function pauseGame() {
            if (gameState.isGameActive) {
                gameState.isGameActive = false;
                levelManager.showFeedback('游戏已暂停 (Game Paused)', 'info');
            } else {
                gameState.isGameActive = true;
                levelManager.showFeedback('游戏继续 (Game Resumed)', 'info');
            }
        }

        function nextLevel() {
            gameState.currentLevel++;
            if (GAME_CONFIG.LEVELS[gameState.currentLevel]) {
                levelManager.loadLevel(gameState.currentLevel);
                showScreen('game');
            } else {
                // Game completed
                showScreen('mainMenu');
                alert('恭喜！您已完成所有关卡！ (Congratulations! You have completed all levels!)');
            }
        }

        function retryLevel() {
            levelManager.loadLevel(gameState.currentLevel);
            showScreen('game');
        }

        function showResults(validationResult) {
            // Update results screen
            document.getElementById('finalScore').textContent = gameState.score;
            document.getElementById('accuracy').textContent = gameState.getAccuracy() + '%';
            document.getElementById('completionTime').textContent = gameState.getCompletionTime();

            // Show appropriate title
            const resultsTitle = document.getElementById('resultsTitle');
            if (validationResult.isValid) {
                resultsTitle.textContent = '完美！ (Perfect!)';
                resultsTitle.style.color = '#00b894';
            } else {
                resultsTitle.textContent = '需要改进 (Needs Improvement)';
                resultsTitle.style.color = '#e74c3c';
            }

            showScreen('results');
        }

        // Initialize the game
        document.addEventListener('DOMContentLoaded', () => {
            try {
                console.log('No Seat, No Peace - Initializing game...');
                gameState.updateUI();

                // Add some welcome feedback
                setTimeout(() => {
                    console.log('Game initialized successfully!');
                    console.log('Available levels:', Object.keys(GAME_CONFIG.LEVELS).length);
                    console.log('Available NPCs:', NPCManager.npcDatabase.length);
                }, 1000);

            } catch (error) {
                console.error('Error initializing game:', error);
                alert('游戏初始化失败，请刷新页面重试。\nGame initialization failed, please refresh the page.');
            }
        });

        // Add global error handler
        window.addEventListener('error', (event) => {
            console.error('Game error:', event.error);
            levelManager.showFeedback('发生错误，请重试 (An error occurred, please try again)', 'warning');
        });
    </script>
</body>
</html>
